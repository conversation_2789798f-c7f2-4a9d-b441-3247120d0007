"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { DayPicker } from "react-day-picker"
import { getHoliday, getAllHolidays, type Holiday } from "@/lib/indian-holidays"
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameMonth, isSameDay, addMonths, subMonths } from 'date-fns';

import { cn } from "@/lib/utils"
import { buttonVariants, Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

export type CalendarProps = React.ComponentProps<typeof DayPicker>

// Helper function to format a date as 'YYYY-MM-DD'
const formatDate = (date: Date): string => {
  const y = date.getFullYear();
  const m = String(date.getMonth() + 1).padStart(2, '0');
  const d = String(date.getDate()).padStart(2, '0');
  return `${y}-${m}-${d}`;
}

const DayContent = ({ date, displayMonth }: { date: Date, displayMonth: Date }) => {
  const holiday = getHoliday(date);
  const isOutside = date.getMonth() !== displayMonth.getMonth();
  const isCurrentMonthHoliday = holiday && !isOutside;
  return (
    <div className={cn(
      "relative w-full h-full flex flex-col items-center justify-start p-1",
      isCurrentMonthHoliday && "bg-red-50 border border-red-200 rounded-lg shadow-sm"
    )}>
      <time
        dateTime={format(date, "yyyy-MM-dd")}
        className={cn(
          "text-sm font-medium mb-1",
          isCurrentMonthHoliday ? "text-red-700" : "text-gray-900"
        )}
      >
        {format(date, "d")}
      </time>
      {isCurrentMonthHoliday && (
        <span className="text-[9px] text-red-600 font-medium text-center leading-tight line-clamp-2 px-1">
          {holiday.name}
        </span>
      )}
    </div>
  );
};


function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  // 1. Keep track of the currently displayed month
  const [month, setMonth] = React.useState<Date>(props.defaultMonth || new Date());

  const getHolidayTypeColor = (type: Holiday['type']) => {
    switch (type) {
      case 'public':
        return 'bg-app-primary text-white';
      case 'religious':
        return 'bg-app-secondary text-white';
      default:
        return 'bg-gray-200 text-gray-700';
    }
  };
  const today = new Date();

  return (
    <div className="relative w-full">
      {/* --- Mobile View --- */}
      <div className="md:hidden">
      <Card className="w-full max-w-sm h-[450px] overflow-hidden shadow-lg border-calendar-border">
        <div className="h-full overflow-y-auto snap-y snap-mandatory">
          {Array.from({ length: 12 }, (_, i) => {
            const monthDate = addMonths(new Date(), i);
            const monthStart = startOfMonth(monthDate);
            const monthEnd = endOfMonth(monthStart);
            const startDate = startOfWeek(monthStart);
            const endDate = endOfWeek(monthEnd);
            
            return (
              <div key={i} className="snap-start p-3 border-b border-calendar-border last:border-b-0">
                {/* Month Header */}
                <div className="text-center mb-3 sticky top-0 bg-card z-10 py-2">
                  <h3 className="text-base font-semibold bg-primary/10 text-primary px-3 py-1 rounded-full inline-block">
                    {format(monthDate, 'MMMM yyyy')}
                  </h3>
                </div>
                
                {/* Days of Week */}
                <div className="grid grid-cols-7 mb-2">
                  {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, idx) => (
                    <div key={idx} className="text-center text-xs font-medium text-muted-foreground p-1">
                      {day}
                    </div>
                  ))}
                </div>
                
                {/* Calendar Grid */}
                <div className="space-y-1">
                  {(() => {
                    const rows = [];
                    let days = [];
                    let day = startDate;
                    
                    while (day <= endDate) {
                      for (let j = 0; j < 7; j++) {
                        const cloneDay = day;
                        const holidays = getAllHolidays(day);
                        const isToday = isSameDay(day, today);
                        const isCurrentMonth = isSameMonth(day, monthStart);
                        const hasHolidays = holidays.length > 0;
                        
                        days.push(
                          <div
                            key={day.toString()}
                            className={cn(
                              "h-14 relative group cursor-pointer transition-all duration-200",
                              isCurrentMonth ? 'bg-card' : 'bg-muted/30',
                              isToday && 'bg-calendar-today ring-1 ring-primary/50',
                              hasHolidays && 'hover:shadow-sm'
                            )}
                          >
                            <div className="h-full flex flex-col p-1 relative">
                              {/* Date Display */}
                              <div className={cn(
                                "font-medium transition-all duration-200",
                                hasHolidays ? 'text-[10px] self-start' : 'text-sm self-center flex-1 flex items-center justify-center',
                                !isCurrentMonth && 'text-muted-foreground',
                                isToday && 'font-bold text-primary',
                                isCurrentMonth && !isToday && 'text-foreground'
                              )}>
                                {format(day, 'd')}
                              </div>
                              
                              {/* Holiday Information */}
                              {hasHolidays && (
                                <div className="flex flex-col gap-0.5 flex-1">
                                  {holidays.slice(0, 1).map((holiday, idx) => (
                                    <span
                                      key={idx}
                                      className={cn(
                                        "text-[8px] px-1 py-0.5 min-h-[12px] max-h-[24px] leading-tight text-center break-words transition-all duration-200",
                                        getHolidayTypeColor(holiday.type)
                                      )}
                                      title={holiday.name}
                                      style={{ 
                                        wordBreak: 'break-word',
                                        overflowWrap: 'break-word',
                                        display: '-webkit-box',
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: 'vertical' as const,
                                        overflow: 'hidden'
                                      }}
                                    >
                                      {holiday.name}
                                    </span>
                                  ))}
                                  {holidays.length > 1 && (
                                    <span
                                      className="text-[8px] px-1 py-0.5 h-3 border-primary/20 border rounded"
                                    >
                                      +{holidays.length - 1}
                                    </span>
                                  )}
                                </div>
                              )}
                              
                              {/* Hover effect */}
                              <div className="absolute inset-0 bg-gradient-to-br from-primary/0 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none" />
                            </div>
                          </div>
                        );
                        day = addDays(day, 1);
                      }
                      
                      rows.push(
                        <div key={day.toString()} className="grid grid-cols-7 gap-0.5">
                          {days}
                        </div>
                      );
                      days = [];
                    }
                    
                    return rows;
                  })()}
                </div>
              </div>
            );
          })}
        </div>
      </Card>
      </div>

      {/* --- Desktop View (Custom Layout) --- */}
      <div className="hidden md:flex justify-center w-full">
        <div className="w-full">
          {(() => {
            // Derived month boundaries for the grid
            const today = new Date();
            const monthStart = startOfMonth(month);
            const monthEnd = endOfMonth(monthStart);
            const gridStart = startOfWeek(monthStart);
            const gridEnd = endOfWeek(monthEnd);

            const selectedDate = (props as any)?.selected as Date | undefined;
            const onSelect = (props as any)?.onSelect as ((date?: Date) => void) | undefined;

            const isDisabled = (date: Date): boolean => {
              const disabled = (props as any)?.disabled;
              if (!disabled) return false;
              if (typeof disabled === 'function') return !!disabled(date);
              return false;
            };

            const getHolidayTypeColor = (type: Holiday['type']) => {
              switch (type) {
                case 'public':
                  return 'bg-app-primary text-white';
                case 'religious':
                  return 'bg-app-secondary text-white';
                default:
                  return 'bg-gray-200 text-gray-700';
              }
            };

            const renderHeader = () => (
              <div className="flex items-center justify-between p-2 bg-white text-black rounded-t-lg">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setMonth(subMonths(month, 1))}
                  className="text-black hover:bg-black/20 h-7 w-7 p-0"
                >
                  <ChevronLeft className="h-3 w-3" />
                </Button>
                <h2 className="text-base font-semibold">
                  {format(month, 'MMMM yyyy')}
                </h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setMonth(addMonths(month, 1))}
                  className="text-black hover:bg-black/20 h-7 w-7 p-0"
                >
                  <ChevronRight className="h-3 w-3" />
                </Button>
              </div>
            );

            const renderDaysOfWeek = () => {
              const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
              return (
                <div className="grid grid-cols-7 border-b border-calendar-border">
                  {daysOfWeek.map((day) => (
                    <div
                      key={day}
                      className={cn(
                        'p-1.5 text-center text-[11px] font-semibold text-black bg-muted/50'
                      )}
                    >
                      {day}
                    </div>
                  ))}
                </div>
              );
            };

            const renderCalendarDays = () => {
              const rows: React.ReactNode[] = [];
              let days: React.ReactNode[] = [];
              let day = gridStart;
              while (day <= gridEnd) {
                for (let i = 0; i < 7; i++) {
                  const holidays = getAllHolidays(day);
                  const hasHolidays = holidays.length > 0;
                  const isToday = isSameDay(day, today);
                  const inCurrentMonth = isSameMonth(day, monthStart);
                  const disabled = isDisabled(day);
                  const selected = selectedDate ? isSameDay(day, selectedDate) : false;

                  days.push(
                    <div
                      key={day.toISOString()}
                      onClick={() => {
                        if (!disabled) {
                          onSelect?.(new Date(day));
                        }
                      }}
                      className={cn(
                        'border-r border-b border-calendar-border transition-all duration-200 relative group',
                        'min-h-[70px]',
                        inCurrentMonth ? 'bg-card' : 'bg-muted/30',
                        isToday && 'bg-calendar-today ring-2 ring-app-primary/50 shadow-sm',
                        hasHolidays && 'hover:shadow-md',
                        disabled ? 'cursor-not-allowed opacity-40' : 'cursor-pointer hover:bg-app-primary/10'
                      )}
                    >
                      <div className={cn('h-full flex flex-col relative p-1.5')}>
                        <div
                          className={cn(
                            'font-medium transition-all duration-200',
                            hasHolidays ? 'text-[12px] mb-1 self-start' : 'text-base self-center flex-1 flex items-center justify-center',
                            !inCurrentMonth && 'text-muted-foreground',
                            isToday && 'font-semibold text-primary',
                            inCurrentMonth && !isToday && 'text-foreground'
                          )}
                        >
                          {format(day, 'd')}
                        </div>

                        {isToday && !hasHolidays && (
                          <div className="absolute inset-0 rounded-sm bg-primary/5 animate-pulse" />
                        )}

                        {hasHolidays && (
                          <div className="flex flex-col gap-1 flex-1">
                            {holidays.slice(0, 1).map((holiday, index) => (
                              <span
                                key={index}
                                className={cn(
                                  'inline-block rounded px-1.5 py-0.5 text-[9px] leading-tight text-center break-words shadow-sm',
                                  getHolidayTypeColor(holiday.type),
                                  'group-hover:shadow-md'
                                )}
                                title={holiday.name}
                                style={{
                                  wordBreak: 'break-word',
                                  overflowWrap: 'break-word',
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: 'vertical' as const,
                                  overflow: 'hidden'
                                }}
                              >
                                {holiday.name}
                              </span>
                            ))}
                            {holidays.length > 1 && (
                              <span className={cn('inline-block rounded border px-1.5 py-0.5 text-[10px]', 'border-primary/20')}>
                                +{holidays.length - 1}
                              </span>
                            )}
                          </div>
                        )}

                        {selected && (
                          <div className="absolute inset-0 ring-2 ring-search-accent/50 rounded-sm pointer-events-none" />
                        )}

                        <div className="absolute inset-0 rounded-sm bg-gradient-to-br from-primary/0 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none" />
                      </div>
                    </div>
                  );

                  day = addDays(day, 1);
                }
                rows.push(
                  <div key={day.toISOString()} className="grid grid-cols-7">
                    {days}
                  </div>
                );
                days = [];
              }
              return rows;
            };

            return (
              <Card className={cn('overflow-hidden shadow-lg border-calendar-border w-[550px] mx-auto')}>
                <div className="h-full flex flex-col">
                  {renderHeader()}
                  {renderDaysOfWeek()}
                  <div className="flex-1 overflow-auto">
                    {renderCalendarDays()}
                  </div>
                </div>
              </Card>
            );
          })()}
        </div>
      </div>
    </div>
  )
}
Calendar.displayName = "Calendar"

export { Calendar }
