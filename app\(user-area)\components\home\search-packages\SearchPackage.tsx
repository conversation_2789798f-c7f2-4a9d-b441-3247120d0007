"use client";

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Search, MapPin, Sparkles } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useDispatch, useSelector } from 'react-redux';
import { changeDate, changeDestination, changeDestinationId } from '@/app/store/features/searchPackageSlice';
import { selectAdultsChild } from '@/app/store/features/roomCapacitySlice';
import { selectTheme, selectThemeId } from '@/app/store/features/selectThemeSlice';
import { Calendar } from '@/components/ui/calendar';
import { startOfToday } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useQuery } from 'react-query';
import { getInterest } from '@/app/actions/get-interest';

// Merged DatePanel
interface DatePanelProps {
  onDateSelect: (date: string) => void;
}

const DatePanel: React.FC<DatePanelProps> = ({ onDateSelect }) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(startOfToday());

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      onDateSelect(date.toLocaleDateString());
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">When?</h3>

      <div className="w-full">
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={handleDateSelect}
          disabled={(date) => date < startOfToday()}
          initialFocus
          className="w-full"
          classNames={{
            table: "w-full border-spacing-1",
            head_cell: "w-full md:w-[40px] font-semibold text-xs md:text-sm text-gray-700 pb-2 text-center",
            cell: "w-full h-8 sm:w-[40px] md:h-[40px] lg:w-[40px] md:h-[40px] p-0.5",
            row: "flex w-full justify-stretch gap-1",
            day: "rounded-lg w-full h-full text-xs md:text-sm font-medium hover:bg-app-primary/10 hover:text-app-primary transition-all duration-200 hover:scale-105 hover:shadow-sm flex items-center justify-center",
            day_selected: "bg-app-secondary text-white rounded-lg shadow-lg hover:shadow-xl transform scale-105",
            day_today: "bg-gradient-to-br from-app-primary to-app-secondary text-white rounded-lg shadow-md",
            day_disabled: "opacity-40 cursor-not-allowed hover:bg-transparent hover:text-gray-400 hover:scale-100",
            caption_label: "text-base md:text-lg font-bold text-gray-800 mb-2",
            nav_button: "rounded-full w-6 h-6 md:w-8 md:h-8 hover:bg-app-primary/10 text-app-primary transition-all duration-200 hover:scale-110 border border-app-primary/30 shadow-sm",
            nav_button_previous: "hover:bg-app-primary/10 border-app-primary/30",
            nav_button_next: "hover:bg-app-primary/10 border-app-primary/30",
            caption: "flex justify-center items-center mb-4 relative",
          }}
        />
      </div>
    </div>
  );
};

// Merged WhoPanel
interface Interest {
  image: string;
  interestId: string;
  interestName: string;
  isFirst: boolean;
  sort: number;
  _id: string;
}

interface WhoPanelProps {
  guests: number;
  onGuestsChange: (guests: number) => void;
}

const WhoPanel: React.FC<WhoPanelProps> = ({ guests, onGuestsChange }) => {
  const [adults, setAdults] = useState(2);
  const [children, setChildren] = useState(0);
  const [rooms, setRooms] = useState(1);
  const [selectedTheme, setSelectedTheme] = useState('Couple');
  const [selectedThemeId, setSelectedThemeId] = useState('');
  const [focused, setFocused] = useState('Couple');
  const [minRooms, setMinRooms] = useState(1);
  const [showChild, setShowChild] = useState(true);

  // Fetch themes
  const { data: themes, isLoading: themesLoading } = useQuery<Interest[]>("fetch Interest", getInterest);

  // Update guests when adults or children change
    useEffect(() => {
    onGuestsChange(adults + children);
  }, [adults, children]);

  // Room calculation logic
  useEffect(() => {
    let r = 0;
    if (children == 0) {
      if (focused !== "Honeymoon") r = Math.ceil(adults / 3);
      else r = Math.ceil(adults / 2);
    } else {
      let x = adults;
      let y = children;
      r = 0;
      while (x >= 3 && y >= 1) {
        x = x - 3;
        y = y - 1;
        r++;
      }
      while (x > 0 || y > 0) {
        x = x - 3;
        y = y - 3;
        r++;
      }
    }
    setRooms(r);
    setMinRooms(r);
  }, [children, adults, focused]);

  useEffect(() => {
    switch (focused) {
      case "Couple": {
        setShowChild(true);
        break;
      }
      case "Honeymoon": {
        setAdults(2)
        setChildren(0);
        setShowChild(false);
        break;
      }
      default: {
        setShowChild(true);
      }
    }
  }, [focused]);

  // Ensure default selected theme id when themes load (for Couple)
  useEffect(() => {
    if (!selectedThemeId && themes && selectedTheme) {
      const found = themes.find(t => t.interestName.toLowerCase() === selectedTheme.toLowerCase());
      if (found) setSelectedThemeId(found.interestId);
    }
  }, [themes, selectedTheme, selectedThemeId]);

  const handleThemeFocus = (theme: Interest) => {
    setFocused(theme.interestName);
    setSelectedTheme(theme.interestName);
    setSelectedThemeId(theme.interestId);

    if (theme.interestName === 'Honeymoon' || theme.interestName === 'Couple') {
      setAdults(2);
      setChildren(0);
    } else if (theme.interestName === 'Family') {
      setAdults(2);
      setChildren(2);
    } else if (theme.interestName === 'Friends') {
      setAdults(4);
      setChildren(0);
    }
  };

  const handleAdultsChange = (newAdults: number) => {
    if (focused === "Honeymoon" && newAdults < 2) {
        return;
    }
    setAdults(newAdults);
  };

  const handleChildrenChange = (newChildren: number) => {
    if (!showChild) {
        setChildren(0);
        return;
    }
    setChildren(newChildren);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Who's coming?</h3>

      <div className="space-y-6">
        {/* Themes Selection */}
        <div>
          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <Sparkles className="w-4 h-4 text-orange-500" />
            Trip Theme
          </h4>
          {themesLoading ? (
            <div className="grid grid-cols-3 gap-2">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="w-full animate-pulse h-16 bg-slate-200 rounded-lg"></div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-3 gap-2 mb-4">
              {themes?.slice(0, 6).map((theme) => {
                const isSelected = selectedTheme === theme.interestName;
                return (
                  <Card
                    key={theme._id}
                    className={cn(
                      'cursor-pointer transition-all duration-300 rounded-lg overflow-hidden group border',
                      isSelected
                        ? 'ring-2 ring-[#ff7865] shadow-md bg-gradient-to-br from-orange-100 via-red-100 to-pink-100 border-[#ff7865]'
                        : 'border-gray-200 hover:border-[#ff7865]/50 hover:shadow-sm'
                    )}
                    onClick={() => handleThemeFocus(theme)}
                  >
                    <CardContent className="p-2 flex flex-col items-center justify-center h-16 relative">
                      <div className="w-6 h-6 mb-1 flex items-center justify-center">
                        {theme.image ? (
                          <img
                            src={`https://tripemilestone.in-maa-1.linodeobjects.com/${theme.image}`}
                            alt={theme.interestName}
                            className="w-full h-full object-contain rounded-full"
                          />
                        ) : (
                          <Sparkles className="w-4 h-4 text-gray-400" />
                        )}
                      </div>
                      <span className="text-xs font-medium text-center text-gray-700">
                        {theme.interestName}
                      </span>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </div>

        {/* Adults */}
        <div className="flex items-center justify-between">
          <div className="flex flex-col mr-auto">
            <div className="font-medium mr-auto">Adults</div>
            <div className="text-sm text-gray-500 mr-auto">Ages 13 or above</div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-red-50 hover:text-red-600 transition-colors"
              onClick={() => handleAdultsChange(Math.max(1, adults - 1))}
              disabled={adults <= 1}
            >
              -
            </Button>
            <span className="w-8 text-center font-medium">{adults}</span>
            <Button
              variant="outline"
              size="sm"
              className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-green-50 hover:text-green-600 transition-colors"
              onClick={() => handleAdultsChange(adults + 1)}
            >
              +
            </Button>
          </div>
        </div>

        {/* Children */}
        {showChild && (
          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              <div className="font-medium">Children</div>
              <div className="text-sm text-gray-500 mr-auto">Ages 5-11</div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-red-50 hover:text-red-600 transition-colors"
                onClick={() => handleChildrenChange(Math.max(0, children - 1))}
                disabled={children <= 0}
              >
                -
              </Button>
              <span className="w-8 text-center font-medium">{children}</span>
              <Button
                variant="outline"
                size="sm"
                className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-green-50 hover:text-green-600 transition-colors"
                onClick={() => handleChildrenChange(children + 1)}
              >
                +
              </Button>
            </div>
          </div>
        )}

        {/* Rooms */}
        <div className="flex items-center justify-between">
          <div className="flex flex-col mr-auto">
            <div className="font-medium mr-auto">Rooms</div>
            <div className="text-sm text-gray-500 mr-auto">Based on guests</div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-red-50 hover:text-red-600 transition-colors"
              onClick={() => setRooms(Math.max(minRooms, rooms - 1))}
              disabled={rooms <= minRooms}
            >
              -
            </Button>
            <span className="w-8 text-center font-medium">{rooms}</span>
            <Button
              variant="outline"
              size="sm"
              className="w-8 h-8 rounded-full p-0 bg-transparent hover:bg-green-50 hover:text-green-600 transition-colors"
              onClick={() => setRooms(Math.min(adults, rooms + 1))}
              disabled={rooms >= adults}
            >
              +
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};


type ActiveSection = 'where' | 'date' | 'who' | null;

interface SearchBarState {
  location: string;
  locationId: string;
  date: string;
  guests: number;
}

interface Destination {
  destinationId: string;
  destinationName: string;
  popular?: boolean;
  isDomestic: boolean;
}

const SearchBar: React.FC = () => {
  const [activeSection, setActiveSection] = useState<ActiveSection>(null);

  // Get today's date and 30 days from now for default date range
  const today = new Date();
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(today.getDate() + 30);

  const [searchData, setSearchData] = useState<SearchBarState>({
    location: 'Manali',
    locationId: '',
    date: `${thirtyDaysFromNow.toLocaleDateString()}`,
    guests: 2
  });

  // State for destination search
  const [searchQuery, setSearchQuery] = useState('');
  const [allDestinations, setAllDestinations] = useState<Destination[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const abortRef = useRef<AbortController | null>(null);

  const containerRef = useRef<HTMLDivElement>(null);
  const whereRef = useRef<HTMLDivElement>(null);
  const dateRef = useRef<HTMLDivElement>(null);
  const whoRef = useRef<HTMLDivElement>(null);
  const [dropdownStyle, setDropdownStyle] = useState({});

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setActiveSection(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Featured destinations for the destination search
  const FEATURED_DESTINATIONS = [
    { name: 'Goa', tag: 'POPULAR', color: 'bg-purple-100 text-purple-800' },
    { name: 'Kashmir', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800' },
    { name: 'Manali', tag: 'HONEYMOON', color: 'bg-green-100 text-green-800' },
    { name: 'Ooty', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800' },
    { name: 'Munnar', tag: 'TRENDING', color: 'bg-blue-100 text-blue-800' },
    { name: 'Andaman', tag: 'IN SEASON', color: 'bg-green-100 text-green-800' },
    { name: 'Kodaikanal', tag: 'IN SEASON', color: 'bg-orange-100 text-orange-800' },
    { name: 'Coorg', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800' },
    { name: 'Alleppey', tag: 'BACKWATERS', color: 'bg-green-100 text-green-800' },
    { name: 'Kochi', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800' },
    { name: 'Shimla', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800' },
    { name: 'Bali', tag: 'HONEYMOON', color: 'bg-green-100 text-green-800' },
    { name: 'Maldives', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800' },
  ];

  const isPopularDestination = (name: string) => {
    return FEATURED_DESTINATIONS.some(featured =>
      name.toLowerCase().includes(featured.name.toLowerCase())
    );
  };

  // Fetch destinations from API
  useEffect(() => {
    if (activeSection !== 'where') return;

    const fetchDestinations = async () => {
      try {
        // Abort previous request if any
        if (abortRef.current) {
          abortRef.current.abort();
        }
        const controller = new AbortController();
        abortRef.current = controller;

        setIsLoading(true);
        let url = 'https://api.tripxplo.com/v1/api/user/package/destination/search';

        if (searchQuery.trim() !== '') {
          url += `?search=${encodeURIComponent(searchQuery)}`;
        }

        const response = await fetch(url, { signal: controller.signal });
        const data = await response.json();
        if (data.result) {
          const destinations = data.result.map((dest: any) => ({
            ...dest,
            popular: isPopularDestination(dest.destinationName),
            isDomestic: dest.destinationType === 'Domestic',
          }));
          setAllDestinations(destinations);
        }
      } catch (error: any) {
        if (error?.name !== 'AbortError') {
          console.error('Error fetching destinations:', error);
        }
      } finally {
        setIsLoading(false);
        abortRef.current = null;
      }
    };

    const timer = setTimeout(() => {
      fetchDestinations();
    }, 300); // Debounce search

    return () => {
      clearTimeout(timer);
      if (abortRef.current) {
        abortRef.current.abort();
      }
    };
  }, [searchQuery, activeSection]);

  // Function to calculate dropdown position based on active section
  const calculateDropdownPosition = useCallback((section: ActiveSection) => {
    if (!section) return {};

    const container = containerRef.current;
    let targetRef;
    let width;

    switch (section) {
      case 'where':
        targetRef = whereRef.current;
        width = '420px';
        break;
      case 'date':
        targetRef = dateRef.current;
        width = '600px';
        break;
      case 'who':
        targetRef = whoRef.current;
        width = '380px';
        break;
      default:
        return {};
    }

    if (targetRef && container) {
      const targetRect = targetRef.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      return {
        left: `${targetRect.left - containerRect.left}px`,
        width: width,
      };
    }

    return {};
  }, []);

  const handleSectionClick = (section: ActiveSection, event: React.MouseEvent<HTMLDivElement>) => {
    const newActiveSection = activeSection === section ? null : section;
    setActiveSection(newActiveSection);

    if (newActiveSection) {
      const style = calculateDropdownPosition(newActiveSection);
      setDropdownStyle(style);
    }
  };

  const handleLocationSelect = useCallback((location: string, locationId: string) => {
    setSearchData(prev => ({ ...prev, location, locationId }));
    setSearchQuery('');
    setActiveSection('date');

    // Recalculate dropdown position for date section
    setTimeout(() => {
      const style = calculateDropdownPosition('date');
      setDropdownStyle(style);
    }, 0);
  }, [calculateDropdownPosition]);

  const handleDateSelect = useCallback((date: string) => {
    // For single date selection, create a range from selected date to 30 days later

    const selectedDate = new Date(date);
    const dateRange = `${selectedDate.toLocaleDateString()}`;

    setSearchData(prev => ({ ...prev, date: dateRange }));
    setActiveSection('who');

    // Recalculate dropdown position for who section
    setTimeout(() => {
      const style = calculateDropdownPosition('who');
      setDropdownStyle(style);
    }, 0);
  }, [calculateDropdownPosition]);

  const handleGuestsChange = useCallback((guests: number) => {
    setSearchData(prev => ({ ...prev, guests }));
  }, []);

  const handleDestinationInputChange = (value: string) => {
    setSearchQuery(value);
    setSearchData(prev => ({ ...prev, location: value }));
  };

  const router = useRouter();
  const dispatch = useDispatch();

  const {
    totalAdults: adults,
    totalChilds: children,
    totalRooms: rooms,
  } = useSelector((state: any) => state.roomSelect?.room) || {
    totalAdults: 2,
    totalChilds: 0,
    totalRooms: 1,
  };

  const { theme: selectedTheme, themeId: selectedThemeId } =
    useSelector((state: any) => state.themeSelect) || {
      theme: 'Couple',
      themeId: '',
    };

  // Initialize default values on component mount
  useEffect(() => {
    // Set default theme if not already set
    if (!selectedTheme || selectedTheme === '') {
      dispatch(selectTheme({ selectedTheme: 'Couple' }));
    }

    // Set default room capacity if not already set
    if (adults === 0) {
      dispatch(
        selectAdultsChild({
          room: {
            adult: 2,
            child: 0,
            room: 1,
          },
        })
      );
    }
  }, [dispatch, selectedTheme, adults]);


  const handleSearch = () => {
    // Prepare final data
    const finalData = {
      destination: searchData.location,
      destinationId: searchData.locationId,
      date: searchData.date,
      rooms: rooms,
      adults: adults,
      children: children,
      theme: selectedTheme,
      themeId: selectedThemeId,
    };

    // Dispatch to Redux store
    dispatch(changeDestination(finalData.destination));
    dispatch(changeDestinationId(finalData.destinationId));
    if (finalData.date && finalData.date !== 'Add dates') {
      dispatch(changeDate(new Date(finalData.date).toISOString()));
    }

    // Dispatch theme to Redux store
    dispatch(selectTheme({ selectedTheme: finalData.theme }));
    dispatch(selectThemeId({ selectedThemeId: finalData.themeId }));

    // Dispatch room and traveler data to Redux store
    dispatch(
      selectAdultsChild({
        room: {
          adult: finalData.adults,
          child: finalData.children,
          room: finalData.rooms,
        },
      })
    );

    // Navigate to packages page
    router.push('/packages');
  };

  const getSectionClasses = (section: ActiveSection) => {
    const baseClasses = "flex-1 px-6 py-3 text-left transition-all duration-300 ease-smooth cursor-pointer";
    const isActive = activeSection === section;

    if (isActive) {
      return `${baseClasses} bg-white shadow-elevated rounded-full z-10 relative`;
    }

    return `${baseClasses} hover:bg-gray-200/50 rounded-full`;
  };

  const getGuestText = () => {
    if (adults === 1 && children === 0) return '1 Adult';
    if (adults > 1 && children === 0) return `${adults} Adults`;
    if (adults === 1 && children === 1) return '1 Adult, 1 Child';
    if (adults === 1 && children > 1) return `1 Adult, ${children} Children`;
    if (adults > 1 && children === 1) return `${adults} Adults, 1 Child`;
    if (adults > 1 && children > 1) return `${adults} Adults, ${children} Children`;
    return '2 Adults'; // Default fallback
  };

  const formatTravelDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = date.toLocaleDateString('en-US', { month: 'short' });
      const year = date.getFullYear();
      return `${day}th ${month} ${year}`;
    } catch {
      return 'N/A';
    }
  };

  return (
    <section id="hero-section" className="relative w-full h-[500px] sm:h-[600px] min-h-[500px] sm:min-h-[600px] flex flex-col items-center justify-center">
      {/* Background with a more vibrant and inviting travel image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat h-full"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1512100356356-de1b84283e18?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/70 h-full"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center px-4 max-w-5xl mx-auto">
        <h1 className="text-4xl md:text-7xl font-semibold text-white mb-4 leading-tight animate-fade-in-up">
          Seamless Travel, <span className="text-emerald-400 font-extrabold italic">Unforgettable</span> Experiences
        </h1>

        {/* New SearchBar Component */}
        <div className="w-full max-w-4xl mx-auto mb-12 mt-8">
          <div ref={containerRef} className="relative w-full max-w-4xl mx-auto">
            {/* Main Search Container */}
            <div className={`rounded-full shadow-xl border border-white/20 flex items-center relative z-20 backdrop-blur-sm transition-all duration-300 ${activeSection ? 'bg-[#ebebeb] shadow-2xl' : 'bg-white shadow-lg hover:shadow-xl'}`}>
              {/* Where Section */}
              <div
                ref={whereRef}
                className={`${getSectionClasses('where')} mr-2`}
                onClick={(e) => handleSectionClick('where', e)}
              >
                <div className="text-xs font-semibold text-search-text mb-1">Where</div>
                {activeSection === 'where' ? (
                  <Input
                    placeholder="Search destinations..."
                    value={searchQuery || searchData.location}
                    onChange={(e) => handleDestinationInputChange(e.target.value)}
                    className="text-sm border-none p-0 h-auto bg-transparent focus:ring-0 focus:border-none shadow-none"
                    autoFocus
                  />
                ) : (
                  <div className="text-sm text-search-secondary truncate">
                    {searchData.location}
                  </div>
                )}
              </div>

              {/* Divider */}
              <div className="w-px h-8 bg-gray-300/50" />

              {/* Date Section */}
              <div
                ref={dateRef}
                className={getSectionClasses('date')}
                onClick={(e) => handleSectionClick('date', e)}
              >
                <div className="text-xs font-semibold text-search-text mb-1">Date</div>
                <div className="text-sm text-search-secondary">
                  {formatTravelDate(searchData.date)}
                </div>
              </div>

              {/* Divider */}
              <div className="w-px h-8 bg-gray-300/50" />

              {/* Who Section */}
              <div
                ref={whoRef}
                className={`${getSectionClasses('who')} flex items-center justify-between`}
                onClick={(e) => handleSectionClick('who', e)}
              >
                <div>
                  <div className="text-xs font-semibold text-search-text mb-1">Who</div>
                  <div className="text-sm text-search-secondary">
                    {getGuestText()}
                  </div>
                </div>
                <button
                  onClick={handleSearch}
                  className={`
                    bg-app-secondary text-search-accent-foreground rounded-full flex items-center justify-center
                    transition-all duration-300 ease-smooth hover:shadow-md
                    ${activeSection === 'who'
                      ? 'px-6 py-3 gap-2'
                      : 'w-12 h-12'
                    }
                  `}
                >
                  <Search className="w-4 h-4" />
                  {activeSection === 'who' && (
                    <span className="text-sm font-semibold whitespace-nowrap">
                      Search
                    </span>
                  )}
                </button>
              </div>
            </div>

            {/* Dropdown Panel */}
            {activeSection && (
              <div
                className="absolute top-full mt-2 z-10 transition-all duration-300 ease-smooth"
                style={dropdownStyle}
              >
                <div className={`bg-white rounded-3xl shadow-2xl border border-gray-100 animate-in slide-in-from-top-2 fade-in duration-300 backdrop-blur-sm ${
                  activeSection === 'where' ? 'p-6 max-h-[40rem] overflow-y-auto' :
                  activeSection === 'date' ? 'p-4' :
                  activeSection === 'who' ? 'p-6' : 'p-6'
                }`}>
                  {activeSection === 'where' && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Where to?</h3>

                      {/* Results */}
                      <div className="max-h-96 overflow-y-auto">
                        {isLoading ? (
                          <div className="space-y-2">
                            {[...Array(6)].map((_, index) => (
                              <div key={index} className="animate-pulse h-12 bg-slate-200 rounded-lg"></div>
                            ))}
                          </div>
                        ) : allDestinations.length > 0 ? (
                          <>
                            {/* Domestic Section */}
                            {allDestinations.filter(d => d.isDomestic).length > 0 && (
                              <div className="mb-4">
                                <h4 className="text-sm font-bold text-blue-600 mb-2 px-3 py-2 bg-blue-50 rounded-lg">
                                  DOMESTIC DESTINATIONS
                                </h4>
                                <div className="space-y-1">
                                  {allDestinations.filter(d => d.isDomestic).sort((a, b) => (b.popular ? 1 : 0) - (a.popular ? 1 : 0)).map((dest) => {
                                    const featured = FEATURED_DESTINATIONS.find(f =>
                                      f.name.toLowerCase() === dest.destinationName.toLowerCase()
                                    );
                                    return (
                                      <button
                                        key={dest.destinationId}
                                        onClick={() => handleLocationSelect(dest.destinationName, dest.destinationId)}
                                        className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors text-left"
                                      >
                                        <div className="flex items-center gap-3">
                                          <MapPin className="w-4 h-4 text-gray-400" />
                                          <span className="text-base text-gray-700">{dest.destinationName}</span>
                                        </div>
                                        {featured && (
                                          <span className={`text-xs font-semibold px-2 py-1 rounded-full ${featured.color}`}>
                                            {featured.tag}
                                          </span>
                                        )}
                                      </button>
                                    );
                                  })}
                                </div>
                              </div>
                            )}

                            {/* International Section */}
                            {allDestinations.filter(d => !d.isDomestic).length > 0 && (
                              <div className="border-t pt-4">
                                <h4 className="text-sm font-bold text-green-600 mb-2 px-3 py-2 bg-green-50 rounded-lg">
                                  INTERNATIONAL DESTINATIONS
                                </h4>
                                <div className="space-y-1">
                                  {allDestinations.filter(d => !d.isDomestic).sort((a, b) => (b.popular ? 1 : 0) - (a.popular ? 1 : 0)).map((dest) => {
                                    const featured = FEATURED_DESTINATIONS.find(f =>
                                      f.name.toLowerCase() === dest.destinationName.toLowerCase()
                                    );
                                    return (
                                      <button
                                        key={dest.destinationId}
                                        onClick={() => handleLocationSelect(dest.destinationName, dest.destinationId)}
                                        className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors text-left"
                                      >
                                        <div className="flex items-center gap-3">
                                          <MapPin className="w-4 h-4 text-gray-400" />
                                          <span className="text-base text-gray-700">{dest.destinationName}</span>
                                        </div>
                                        {featured && (
                                          <span className={`text-xs font-semibold px-2 py-1 rounded-full ${featured.color}`}>
                                            {featured.tag}
                                          </span>
                                        )}
                                      </button>
                                    );
                                  })}
                                </div>
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="text-center py-8">
                            <MapPin className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                            <p className="text-sm text-gray-500">No destinations found</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  {activeSection === 'date' && (
                    <DatePanel onDateSelect={handleDateSelect} />
                  )}
                  {activeSection === 'who' && (
                    <WhoPanel
                      guests={searchData.guests}
                      onGuestsChange={handleGuestsChange}
                    />
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Enhanced floating elements for a more dynamic feel */}
      <div className="absolute top-20 left-10 w-28 h-28 bg-emerald-400/20 backdrop-blur-sm rounded-full animate-pulse-slow hidden lg:block"></div>
      <div className="absolute bottom-32 right-16 w-24 h-24 bg-blue-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-700 hidden lg:block"></div>
      <div className="absolute top-1/3 right-24 w-20 h-20 bg-purple-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-300 hidden lg:block"></div>
      <div className="absolute bottom-1/4 left-24 w-16 h-16 bg-yellow-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-1000 hidden lg:block"></div>
    </section>
  );
};



export default SearchBar;